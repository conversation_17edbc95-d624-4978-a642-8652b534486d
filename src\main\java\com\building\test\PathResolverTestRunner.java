package com.building.test;

import com.building.util.ProjectPathResolver;
import com.building.util.ProjectPathResolver.ProjectPathException;
import com.building.util.ProjectPathResolver.ValidationResult;

import java.io.File;

/**
 * 路径解析器测试运行器
 * 用于验证改进后的路径处理功能
 */
public class PathResolverTestRunner {
    
    public static void main(String[] args) {
        System.out.println("=== ProjectPathResolver 功能测试 ===");
        System.out.println("当前工作目录: " + System.getProperty("user.dir"));
        System.out.println("Java版本: " + System.getProperty("java.version"));
        System.out.println("操作系统: " + System.getProperty("os.name"));
        System.out.println();
        
        // 测试1: 获取项目根目录
        testGetProjectRootDirectory();
        
        // 测试2: 验证项目根目录
        testValidateProjectRoot();
        
        // 测试3: 测试目录创建
        testEnsureDirectoryExists();
        
        // 测试4: 测试相对路径获取
        testGetProjectRelativePath();
        
        // 测试5: 测试项目根目录识别
        testIsProjectRoot();
        
        System.out.println("=== 测试完成 ===");
    }
    
    private static void testGetProjectRootDirectory() {
        System.out.println("--- 测试1: 获取项目根目录 ---");
        
        try {
            String projectRoot = ProjectPathResolver.getProjectRootDirectory();
            System.out.println("✓ 成功获取项目根目录: " + projectRoot);
            
            // 检查关键文件是否存在
            File streamServerFile = new File(projectRoot, "stream-server.js");
            File packageJsonFile = new File(projectRoot, "package.json");
            File pomXmlFile = new File(projectRoot, "pom.xml");
            
            System.out.println("  - stream-server.js 存在: " + streamServerFile.exists());
            System.out.println("  - package.json 存在: " + packageJsonFile.exists());
            System.out.println("  - pom.xml 存在: " + pomXmlFile.exists());
            
        } catch (ProjectPathException e) {
            System.err.println("✗ 获取项目根目录失败: " + e.getMessage());
            System.err.println("  尝试过的路径:");
            for (String path : e.getAttemptedPaths()) {
                System.err.println("    - " + path);
            }
        } catch (Exception e) {
            System.err.println("✗ 未预期的异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
    
    private static void testValidateProjectRoot() {
        System.out.println("--- 测试2: 验证项目根目录 ---");
        
        try {
            String projectRoot = ProjectPathResolver.getProjectRootDirectory();
            ValidationResult result = ProjectPathResolver.validateProjectRoot(projectRoot);
            
            System.out.println("验证结果: " + (result.isValid() ? "✓ 有效" : "✗ 无效"));
            System.out.println("项目根目录: " + result.getProjectRoot());
            
            if (!result.getErrors().isEmpty()) {
                System.out.println("错误:");
                for (String error : result.getErrors()) {
                    System.out.println("  ✗ " + error);
                }
            }
            
            if (!result.getWarnings().isEmpty()) {
                System.out.println("警告:");
                for (String warning : result.getWarnings()) {
                    System.out.println("  ⚠ " + warning);
                }
            }
            
            if (!result.getSuccesses().isEmpty()) {
                System.out.println("成功:");
                for (String success : result.getSuccesses()) {
                    System.out.println("  ✓ " + success);
                }
            }
            
        } catch (ProjectPathException e) {
            System.err.println("✗ 无法获取项目根目录进行验证: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("✗ 验证过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
    
    private static void testEnsureDirectoryExists() {
        System.out.println("--- 测试3: 测试目录创建 ---");
        
        try {
            String projectRoot = ProjectPathResolver.getProjectRootDirectory();
            String testDir = projectRoot + File.separator + "test-temp-dir";
            
            // 确保目录不存在
            File dir = new File(testDir);
            if (dir.exists()) {
                dir.delete();
            }
            
            System.out.println("测试目录: " + testDir);
            System.out.println("目录创建前存在: " + dir.exists());
            
            boolean created = ProjectPathResolver.ensureDirectoryExists(testDir);
            System.out.println("创建结果: " + (created ? "✓ 成功" : "✗ 失败"));
            System.out.println("目录创建后存在: " + dir.exists());
            
            // 清理测试目录
            if (dir.exists()) {
                dir.delete();
                System.out.println("清理测试目录: ✓ 完成");
            }
            
        } catch (ProjectPathException e) {
            System.err.println("✗ 无法获取项目根目录进行测试: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("✗ 目录创建测试异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
    
    private static void testGetProjectRelativePath() {
        System.out.println("--- 测试4: 测试相对路径获取 ---");
        
        try {
            String relativePath = ProjectPathResolver.getProjectRelativePath("stream-server.js");
            System.out.println("✓ stream-server.js 完整路径: " + relativePath);
            
            File file = new File(relativePath);
            System.out.println("  文件存在: " + file.exists());
            System.out.println("  文件可读: " + file.canRead());
            
            // 测试其他文件
            String packageJsonPath = ProjectPathResolver.getProjectRelativePath("package.json");
            System.out.println("✓ package.json 完整路径: " + packageJsonPath);
            
        } catch (ProjectPathException e) {
            System.err.println("✗ 获取相对路径失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("✗ 相对路径测试异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
    
    private static void testIsProjectRoot() {
        System.out.println("--- 测试5: 测试项目根目录识别 ---");
        
        try {
            String projectRoot = ProjectPathResolver.getProjectRootDirectory();
            File projectDir = new File(projectRoot);
            
            boolean isRoot = ProjectPathResolver.isProjectRoot(projectDir);
            System.out.println("项目根目录识别: " + (isRoot ? "✓ 正确识别" : "✗ 识别失败"));
            
            // 测试父目录（应该不是项目根目录）
            File parentDir = projectDir.getParentFile();
            if (parentDir != null) {
                boolean isParentRoot = ProjectPathResolver.isProjectRoot(parentDir);
                System.out.println("父目录识别: " + (!isParentRoot ? "✓ 正确排除" : "✗ 错误识别"));
            }
            
            // 测试不存在的目录
            File nonExistentDir = new File("/non/existent/directory");
            boolean isNonExistentRoot = ProjectPathResolver.isProjectRoot(nonExistentDir);
            System.out.println("不存在目录识别: " + (!isNonExistentRoot ? "✓ 正确排除" : "✗ 错误识别"));
            
        } catch (ProjectPathException e) {
            System.err.println("✗ 无法获取项目根目录进行测试: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("✗ 项目根目录识别测试异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
}
